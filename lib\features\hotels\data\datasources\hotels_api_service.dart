import '../../../../core/network/dio_client.dart';

class HotelsApiService {
  final DioClient _dioClient;

  HotelsApiService(this._dioClient);

  // Mock API data for Egyptian hotels
  Future<List<Map<String, dynamic>>> getHotels() async {
    // Simulate API delay
    await Future.delayed(const Duration(seconds: 2));
    
    return [
      {
        'id': '1',
        'name': 'فندق الأهرامات الذهبية',
        'location': 'الجيزة، مصر',
        'rating': 4.8,
        'price': 1200.0,
        'imageUrl': 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=500',
        'amenities': ['واي فاي مجاني', 'مسبح', 'جيم', 'مطعم', 'سبا'],
        'description': 'فندق فاخر بإطلالة رائعة على الأهرامات',
        'address': 'شارع الهرم، الجيزة',
        'phone': '+20 2 1234 5678',
        'email': '<EMAIL>',
        'checkInTime': '14:00',
        'checkOutTime': '12:00',
        'latitude': 29.9792,
        'longitude': 31.1342,
        'totalRooms': 150,
        'availableRooms': 25,
        'category': 'فاخر',
        'images': [
          'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=500',
          'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=500',
          'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=500'
        ]
      },
      {
        'id': '2',
        'name': 'منتجع البحر الأحمر الملكي',
        'location': 'الغردقة، مصر',
        'rating': 4.9,
        'price': 2500.0,
        'imageUrl': 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=500',
        'amenities': ['شاطئ خاص', 'سبا', 'مطعم', 'بار', 'رياضات مائية'],
        'description': 'منتجع ساحلي فاخر على البحر الأحمر',
        'address': 'الغردقة، البحر الأحمر',
        'phone': '+20 65 1234 5678',
        'email': '<EMAIL>',
        'checkInTime': '15:00',
        'checkOutTime': '11:00',
        'latitude': 27.2579,
        'longitude': 33.8116,
        'totalRooms': 200,
        'availableRooms': 45,
        'category': 'منتجع',
        'images': [
          'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=500',
          'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=500',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500'
        ]
      },
      {
        'id': '3',
        'name': 'فندق النيل الأزرق',
        'location': 'القاهرة، مصر',
        'rating': 4.6,
        'price': 800.0,
        'imageUrl': 'https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=500',
        'amenities': ['إطلالة على النيل', 'مطعم', 'واي فاي مجاني', 'خدمة الغرف'],
        'description': 'فندق أنيق بإطلالة خلابة على نهر النيل',
        'address': 'كورنيش النيل، القاهرة',
        'phone': '+20 2 9876 5432',
        'email': '<EMAIL>',
        'checkInTime': '14:00',
        'checkOutTime': '12:00',
        'latitude': 30.0444,
        'longitude': 31.2357,
        'totalRooms': 120,
        'availableRooms': 18,
        'category': 'متوسط',
        'images': [
          'https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=500',
          'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=500',
          'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=500'
        ]
      },
      {
        'id': '4',
        'name': 'فندق الإسكندرية التاريخي',
        'location': 'الإسكندرية، مصر',
        'rating': 4.4,
        'price': 600.0,
        'imageUrl': 'https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?w=500',
        'amenities': ['موقع تاريخي', 'مطعم', 'واي فاي مجاني', 'إطلالة على البحر'],
        'description': 'فندق تاريخي في قلب الإسكندرية',
        'address': 'الكورنيش، الإسكندرية',
        'phone': '+20 3 1111 2222',
        'email': '<EMAIL>',
        'checkInTime': '14:00',
        'checkOutTime': '12:00',
        'latitude': 31.2001,
        'longitude': 29.9187,
        'totalRooms': 80,
        'availableRooms': 12,
        'category': 'تاريخي',
        'images': [
          'https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?w=500',
          'https://images.unsplash.com/photo-1445019980597-93fa8acb246c?w=500',
          'https://images.unsplash.com/photo-1584132967334-10e028bd69f7?w=500'
        ]
      },
      {
        'id': '5',
        'name': 'منتجع شرم الشيخ الاستوائي',
        'location': 'شرم الشيخ، مصر',
        'rating': 4.7,
        'price': 1800.0,
        'imageUrl': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500',
        'amenities': ['شاطئ خاص', 'غوص', 'سبا', 'مطاعم متعددة', 'ملاعب تنس'],
        'description': 'منتجع استوائي فاخر في شرم الشيخ',
        'address': 'خليج نعمة، شرم الشيخ',
        'phone': '+20 69 3333 4444',
        'email': '<EMAIL>',
        'checkInTime': '15:00',
        'checkOutTime': '11:00',
        'latitude': 27.9158,
        'longitude': 34.3300,
        'totalRooms': 300,
        'availableRooms': 67,
        'category': 'منتجع',
        'images': [
          'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500',
          'https://images.unsplash.com/photo-1582719508461-905c673771fd?w=500',
          'https://images.unsplash.com/photo-1540541338287-41700207dee6?w=500'
        ]
      },
      {
        'id': '6',
        'name': 'فندق أسوان النوبي',
        'location': 'أسوان، مصر',
        'rating': 4.3,
        'price': 500.0,
        'imageUrl': 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500',
        'amenities': ['تراث نوبي', 'مطعم تقليدي', 'واي فاي مجاني', 'رحلات نيلية'],
        'description': 'فندق بطابع نوبي أصيل في أسوان',
        'address': 'جزيرة إلفنتين، أسوان',
        'phone': '+20 97 5555 6666',
        'email': '<EMAIL>',
        'checkInTime': '14:00',
        'checkOutTime': '12:00',
        'latitude': 24.0889,
        'longitude': 32.8998,
        'totalRooms': 60,
        'availableRooms': 8,
        'category': 'تراثي',
        'images': [
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500',
          'https://images.unsplash.com/photo-1566665797739-1674de7a421a?w=500',
          'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=500'
        ]
      },
      {
        'id': '7',
        'name': 'فندق الأقصر الفرعوني',
        'location': 'الأقصر، مصر',
        'rating': 4.5,
        'price': 700.0,
        'imageUrl': 'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=500',
        'amenities': ['موقع أثري', 'مطعم', 'مسبح', 'رحلات أثرية', 'واي فاي مجاني'],
        'description': 'فندق بتصميم فرعوني في مدينة الأقصر الأثرية',
        'address': 'الضفة الشرقية، الأقصر',
        'phone': '+20 95 7777 8888',
        'email': '<EMAIL>',
        'checkInTime': '14:00',
        'checkOutTime': '12:00',
        'latitude': 25.6872,
        'longitude': 32.6396,
        'totalRooms': 100,
        'availableRooms': 22,
        'category': 'أثري',
        'images': [
          'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=500',
          'https://images.unsplash.com/photo-1584132967334-10e028bd69f7?w=500',
          'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=500'
        ]
      },
      {
        'id': '8',
        'name': 'فندق العلمين الساحلي',
        'location': 'العلمين الجديدة، مصر',
        'rating': 4.2,
        'price': 900.0,
        'imageUrl': 'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=500',
        'amenities': ['شاطئ رملي', 'مطعم', 'مسبح', 'ملاعب رياضية', 'واي فاي مجاني'],
        'description': 'فندق ساحلي حديث في العلمين الجديدة',
        'address': 'العلمين الجديدة، مطروح',
        'phone': '+20 46 9999 0000',
        'email': '<EMAIL>',
        'checkInTime': '15:00',
        'checkOutTime': '11:00',
        'latitude': 30.8025,
        'longitude': 28.9512,
        'totalRooms': 180,
        'availableRooms': 35,
        'category': 'ساحلي',
        'images': [
          'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=500',
          'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=500',
          'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=500'
        ]
      }
    ];
  }

  Future<List<Map<String, dynamic>>> searchHotels({
    String? query,
    String? location,
    double? minPrice,
    double? maxPrice,
    double? minRating,
    List<String>? amenities,
  }) async {
    final allHotels = await getHotels();
    
    return allHotels.where((hotel) {
      if (query != null && query.isNotEmpty) {
        if (!hotel['name'].toString().toLowerCase().contains(query.toLowerCase()) &&
            !hotel['location'].toString().toLowerCase().contains(query.toLowerCase())) {
          return false;
        }
      }
      
      if (location != null && location.isNotEmpty) {
        if (!hotel['location'].toString().toLowerCase().contains(location.toLowerCase())) {
          return false;
        }
      }
      
      if (minPrice != null && hotel['price'] < minPrice) {
        return false;
      }
      
      if (maxPrice != null && hotel['price'] > maxPrice) {
        return false;
      }
      
      if (minRating != null && hotel['rating'] < minRating) {
        return false;
      }
      
      if (amenities != null && amenities.isNotEmpty) {
        final hotelAmenities = List<String>.from(hotel['amenities']);
        if (!amenities.any((amenity) => hotelAmenities.contains(amenity))) {
          return false;
        }
      }
      
      return true;
    }).toList();
  }

  Future<Map<String, dynamic>?> getHotelById(String id) async {
    final hotels = await getHotels();
    try {
      return hotels.firstWhere((hotel) => hotel['id'] == id);
    } catch (e) {
      return null;
    }
  }
}
