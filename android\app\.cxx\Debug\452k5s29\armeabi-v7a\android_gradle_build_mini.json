{"buildFiles": ["C:\\Users\\<USER>\\Downloads\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\untitled7\\android\\app\\.cxx\\Debug\\452k5s29\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\untitled7\\android\\app\\.cxx\\Debug\\452k5s29\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}