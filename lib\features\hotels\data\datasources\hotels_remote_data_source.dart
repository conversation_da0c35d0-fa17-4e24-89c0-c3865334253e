import 'dart:convert';
import 'package:http/http.dart' as http;

// Models
class Hotel {
  final String id;
  final String name;
  final String description;
  final String address;
  final double rating;
  final double price;
  final List<String> amenities;
  final List<String> images;
  final String city;
  final String country;

  Hotel({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.rating,
    required this.price,
    required this.amenities,
    required this.images,
    required this.city,
    required this.country,
  });

  factory Hotel.fromJson(Map<String, dynamic> json) {
    return Hotel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      address: json['address'] ?? '',
      rating: (json['rating'] ?? 0.0).toDouble(),
      price: (json['price'] ?? 0.0).toDouble(),
      amenities: List<String>.from(json['amenities'] ?? []),
      images: List<String>.from(json['images'] ?? []),
      city: json['city'] ?? '',
      country: json['country'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'address': address,
      'rating': rating,
      'price': price,
      'amenities': amenities,
      'images': images,
      'city': city,
      'country': country,
    };
  }
}

class HotelSearchRequest {
  final String? destination;
  final DateTime? checkIn;
  final DateTime? checkOut;
  final int guests;
  final int rooms;
  final double? minPrice;
  final double? maxPrice;
  final double? minRating;

  HotelSearchRequest({
    this.destination,
    this.checkIn,
    this.checkOut,
    this.guests = 1,
    this.rooms = 1,
    this.minPrice,
    this.maxPrice,
    this.minRating,
  });

  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{};
    
    if (destination != null) params['destination'] = destination;
    if (checkIn != null) params['check_in'] = checkIn!.toIso8601String();
    if (checkOut != null) params['check_out'] = checkOut!.toIso8601String();
    params['guests'] = guests.toString();
    params['rooms'] = rooms.toString();
    if (minPrice != null) params['min_price'] = minPrice.toString();
    if (maxPrice != null) params['max_price'] = maxPrice.toString();
    if (minRating != null) params['min_rating'] = minRating.toString();
    
    return params;
  }
}

// Exceptions
class NetworkException implements Exception {
  final String message;
  NetworkException(this.message);
}

class ServerException implements Exception {
  final String message;
  final int statusCode;
  ServerException(this.message, this.statusCode);
}

class ParseException implements Exception {
  final String message;
  ParseException(this.message);
}

// Abstract interface
abstract class HotelsRemoteDataSource {
  Future<List<Hotel>> searchHotels(HotelSearchRequest request);
  Future<Hotel> getHotelById(String hotelId);
  Future<List<Hotel>> getFeaturedHotels();
  Future<List<Hotel>> getHotelsByCity(String city);
  Future<List<String>> getPopularDestinations();
}

// Implementation
class HotelsRemoteDataSourceImpl implements HotelsRemoteDataSource {
  final http.Client client;
  final String baseUrl;
  final String apiKey;

  HotelsRemoteDataSourceImpl({
    required this.client,
    required this.baseUrl,
    required this.apiKey, required Object dioClient,
  });

  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer $apiKey',
  };

  @override
  Future<List<Hotel>> searchHotels(HotelSearchRequest request) async {
    try {
      final queryParams = request.toQueryParams();
      final uri = Uri.parse('$baseUrl/hotels/search').replace(
        queryParameters: queryParams.map((k, v) => MapEntry(k, v.toString())),
      );

      final response = await client.get(uri, headers: _headers);

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final List<dynamic> hotelsJson = data['hotels'] ?? [];
        
        return hotelsJson.map((json) => Hotel.fromJson(json)).toList();
      } else {
        throw ServerException(
          'Failed to search hotels: ${response.body}',
          response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw NetworkException('Network error occurred: $e');
    }
  }

  @override
  Future<Hotel> getHotelById(String hotelId) async {
    try {
      final uri = Uri.parse('$baseUrl/hotels/$hotelId');
      final response = await client.get(uri, headers: _headers);

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return Hotel.fromJson(data);
      } else if (response.statusCode == 404) {
        throw ServerException('Hotel not found', response.statusCode);
      } else {
        throw ServerException(
          'Failed to get hotel: ${response.body}',
          response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw NetworkException('Network error occurred: $e');
    }
  }

  @override
  Future<List<Hotel>> getFeaturedHotels() async {
    try {
      final uri = Uri.parse('$baseUrl/hotels/featured');
      final response = await client.get(uri, headers: _headers);

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final List<dynamic> hotelsJson = data['hotels'] ?? [];
        
        return hotelsJson.map((json) => Hotel.fromJson(json)).toList();
      } else {
        throw ServerException(
          'Failed to get featured hotels: ${response.body}',
          response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw NetworkException('Network error occurred: $e');
    }
  }

  @override
  Future<List<Hotel>> getHotelsByCity(String city) async {
    try {
      final uri = Uri.parse('$baseUrl/hotels/city/$city');
      final response = await client.get(uri, headers: _headers);

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final List<dynamic> hotelsJson = data['hotels'] ?? [];
        
        return hotelsJson.map((json) => Hotel.fromJson(json)).toList();
      } else {
        throw ServerException(
          'Failed to get hotels by city: ${response.body}',
          response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw NetworkException('Network error occurred: $e');
    }
  }

  @override
  Future<List<String>> getPopularDestinations() async {
    try {
      final uri = Uri.parse('$baseUrl/destinations/popular');
      final response = await client.get(uri, headers: _headers);

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final List<dynamic> destinationsJson = data['destinations'] ?? [];
        
        return destinationsJson.cast<String>();
      } else {
        throw ServerException(
          'Failed to get popular destinations: ${response.body}',
          response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw NetworkException('Network error occurred: $e');
    }
  }
}

// Factory for creating the data source
class HotelsRemoteDataSourceFactory {
  static HotelsRemoteDataSource create({
    required String baseUrl,
    required String apiKey,
    http.Client? client,
  }) {
    return HotelsRemoteDataSourceImpl(
      client: client ?? http.Client(),
      baseUrl: baseUrl,
      apiKey: apiKey,
      dioClient: Object(),
    );
  }
}
