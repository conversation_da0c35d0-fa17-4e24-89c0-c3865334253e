import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled7/features/hotels/presentation/pages/hotel_details_page.dart';
import 'package:untitled7/features/hotels/presentation/pages/hotels_list_page.dart';
import 'injection_container.dart' as di;
import 'core/constants/color_constants.dart';

import 'features/authentication/presentation/pages/splash_page.dart';
import 'features/authentication/presentation/pages/login_page.dart';
import 'features/authentication/presentation/pages/register_page.dart';

import 'features/authentication/presentation/bloc/auth_bloc.dart';
import 'features/hotels/presentation/bloc/hotels_bloc.dart';
import 'features/bookings/presentation/bloc/booking_bloc.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  await di.initDependencies();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => di.sl<AuthBloc>()),
        BlocProvider(create: (_) => di.sl<HotelsBloc>()),
        BlocProvider(create: (_) => di.sl<BookingBloc>()),
      ],
      child: MaterialApp(
        title: 'Hotel Booking App',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primaryColor: ColorConstants.primaryColor,
          scaffoldBackgroundColor: ColorConstants.backgroundColor,
        ),
        initialRoute: '/',
        routes: {
          '/': (context) => const SplashPage(),
          '/login': (context) => const LoginPage(),
          '/register': (context) => const RegisterPage(),
          '/HotelsListPage': (context) => const HotelsListPage(),
          '/hotel-details': (context) =>  HotelDetailsPage(),
        },
      ),
    );
  }
}
