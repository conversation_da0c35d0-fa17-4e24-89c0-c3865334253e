import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'injection_container.dart' as di;
import 'core/constants/color_constants.dart';

// Authentication pages
import 'features/authentication/presentation/pages/splash_page.dart';
import 'features/authentication/presentation/pages/login_page.dart';
import 'features/authentication/presentation/pages/register_page.dart';

// Home page
import 'features/home/<USER>/pages/home_page.dart';

// Hotel pages
import 'features/hotels/presentation/pages/hotel_details_page.dart';
import 'features/hotels/presentation/pages/hotels_list_page.dart';
import 'features/hotels/presentation/pages/favorites_page.dart';
import 'features/hotels/presentation/pages/search_hotels_page.dart';

// Booking pages
import 'features/bookings/presentation/pages/booking_page.dart';
import 'features/bookings/presentation/pages/my_bookings_page.dart';
import 'features/bookings/presentation/pages/booking_confirmation_page.dart';

// Profile pages
import 'features/profile/presentation/pages/profile_page.dart';
import 'features/profile/presentation/pages/edit_profile_page.dart';

// Settings pages
import 'features/settings/presentation/pages/settings_page.dart';

// Blocs
import 'features/authentication/presentation/bloc/auth_bloc.dart';
import 'features/hotels/presentation/bloc/hotels_bloc.dart';
import 'features/bookings/presentation/bloc/booking_bloc.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  await di.initDependencies();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => di.sl<AuthBloc>()),
        BlocProvider(create: (_) => di.sl<HotelsBloc>()),
        BlocProvider(create: (_) => di.sl<BookingBloc>()),
      ],
      child: MaterialApp(
        title: 'Hotel Booking App',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primaryColor: ColorConstants.primaryColor,
          scaffoldBackgroundColor: ColorConstants.backgroundColor,
        ),
        initialRoute: '/',
        routes: {
          '/': (context) => const SplashPage(),
          '/login': (context) => const LoginPage(),
          '/register': (context) => const RegisterPage(),
          '/home': (context) => const HomePage(),
          '/hotels': (context) => const HotelsListPage(),
          '/HotelsListPage': (context) => const HotelsListPage(), // Keep for backward compatibility
          '/hotel-details': (context) => HotelDetailsPage(),
          '/favorites': (context) => const FavoritesPage(),
          '/my-bookings': (context) => const MyBookingsPage(),
          '/profile': (context) => const ProfilePage(),
          '/settings': (context) => const SettingsPage(),
          '/search-hotels': (context) => const SearchHotelsPage(),
          '/edit-profile': (context) => const EditProfilePage(),
        },
        onGenerateRoute: (settings) {
          // Handle routes that need arguments
          switch (settings.name) {
            case '/home':
              final initialIndex = settings.arguments as int? ?? 0;
              return MaterialPageRoute(
                builder: (context) => HomePage(initialIndex: initialIndex),
              );
            case '/hotel-details':
              final hotelId = settings.arguments as String?;
              return MaterialPageRoute(
                builder: (context) => HotelDetailsPage(hotelId: hotelId),
              );
            case '/booking':
              final args = settings.arguments as Map<String, dynamic>?;
              return MaterialPageRoute(
                builder: (context) => BookingPage(
                  hotelId: args?['hotelId'] ?? '',
                  hotelName: args?['hotelName'] ?? '',
                  pricePerNight: args?['pricePerNight'] ?? 0.0,
                ),
              );
            case '/booking-confirmation':
              final bookingData = settings.arguments as Map<String, dynamic>? ?? {};
              return MaterialPageRoute(
                builder: (context) => BookingConfirmationPage(
                  bookingData: bookingData,
                ),
              );
            default:
              return null;
          }
        },
      ),
    );
  }
}
