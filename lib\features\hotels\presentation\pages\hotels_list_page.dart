import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled7/core/constants/color_constants.dart';
import '../bloc/hotels_bloc.dart';
import '../widgets/hotel_card.dart';
import '../../domain/entities/hotel.dart';

class HotelsListPage extends StatefulWidget {
  const HotelsListPage({super.key});

  @override
  State<HotelsListPage> createState() => _HotelsListPageState();
}

class _HotelsListPageState extends State<HotelsListPage> {
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounceTimer;
  String _selectedCategory = 'الكل';
  String _sortBy = 'السعر';

  final List<String> _categories = [
    'الكل',
    'فاخر',
    'منتجع',
    'متوسط',
    'تاريخي',
    'تراثي',
    'أثري',
    'ساحلي',
  ];

  final List<String> _sortOptions = ['السعر', 'التقييم', 'الاسم', 'المسافة'];

  @override
  void initState() {
    super.initState();
    // Load hotels on page open
    context.read<HotelsBloc>().add(LoadHotels());
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'الفنادق',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: ColorConstants.primaryColor,
        foregroundColor: ColorConstants.onPrimaryColor,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () async {
              final result = await Navigator.pushNamed(
                context,
                '/search-hotels',
              );
              if (result != null && mounted) {
                final searchCriteria = result as Map<String, dynamic>;
                if (mounted) {
                  context.read<HotelsBloc>().add(
                    FilterHotels(
                      query: searchCriteria['searchQuery'],
                      location: searchCriteria['location'],
                      minPrice: searchCriteria['minPrice'],
                      maxPrice: searchCriteria['maxPrice'],
                      minRating: searchCriteria['minRating'],
                      amenities: List<String>.from(
                        searchCriteria['amenities'] ?? [],
                      ),
                    ),
                  );
                }
              }
            },
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            onSelected: (value) {
              setState(() {
                _sortBy = value;
              });
              _applySorting();
            },
            itemBuilder:
                (context) =>
                    _sortOptions.map((option) {
                      return PopupMenuItem<String>(
                        value: option,
                        child: Row(
                          children: [
                            Icon(
                              _getSortIcon(option),
                              size: 20,
                              color:
                                  _sortBy == option
                                      ? ColorConstants.primaryColor
                                      : Colors.grey,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              option,
                              style: TextStyle(
                                color:
                                    _sortBy == option
                                        ? ColorConstants.primaryColor
                                        : Colors.black,
                                fontWeight:
                                    _sortBy == option
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          Expanded(
            child: BlocBuilder<HotelsBloc, HotelsState>(
              builder: (context, state) {
                if (state is HotelsLoading) {
                  return _buildLoadingWidget();
                } else if (state is HotelsError) {
                  return _buildErrorWidget(state.message);
                } else if (state is HotelsLoaded) {
                  if (state.hotels.isEmpty) {
                    return _buildEmptyWidget();
                  }
                  return _buildHotelsList(state.hotels);
                }
                return const SizedBox.shrink();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'ابحث عن فندق...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon:
                  _searchController.text.isNotEmpty
                      ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          context.read<HotelsBloc>().add(LoadHotels());
                        },
                      )
                      : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.grey.shade100,
            ),
            onChanged: _onSearchChanged,
          ),
          const SizedBox(height: 12),
          // Category Filter
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = _selectedCategory == category;
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(category),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = category;
                      });
                      _applyFilters();
                    },
                    selectedColor: ColorConstants.primaryColor.withValues(
                      alpha: 0.2,
                    ),
                    checkmarkColor: ColorConstants.primaryColor,
                    labelStyle: TextStyle(
                      color:
                          isSelected
                              ? ColorConstants.primaryColor
                              : Colors.grey.shade700,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('جاري تحميل الفنادق...'),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.hotel_outlined, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد فنادق متاحة',
            style: TextStyle(fontSize: 18, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير معايير البحث',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                context.read<HotelsBloc>().add(LoadHotels());
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorConstants.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHotelsList(List<Hotel> hotels) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<HotelsBloc>().add(LoadHotels());
      },
      child: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: hotels.length,
        separatorBuilder: (context, index) => const SizedBox(height: 16),
        itemBuilder: (context, index) {
          final hotel = hotels[index];
          return HotelCard(
            hotel: hotel,
            onTap: () {
              Navigator.pushNamed(
                context,
                '/hotel-details',
                arguments: hotel.id,
              );
            },
          );
        },
      ),
    );
  }

  void _onSearchChanged(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (query.isNotEmpty) {
        context.read<HotelsBloc>().add(SearchHotels(query: query));
      } else {
        context.read<HotelsBloc>().add(LoadHotels());
      }
    });
  }

  void _applyFilters() {
    if (_selectedCategory == 'الكل') {
      context.read<HotelsBloc>().add(LoadHotels());
    } else {
      // Apply category filter - this would need to be implemented in the API
      context.read<HotelsBloc>().add(LoadHotels());
    }
  }

  void _applySorting() {
    // Apply sorting - this would need to be implemented in the API
    context.read<HotelsBloc>().add(LoadHotels());
  }

  IconData _getSortIcon(String sortOption) {
    switch (sortOption) {
      case 'السعر':
        return Icons.attach_money;
      case 'التقييم':
        return Icons.star;
      case 'الاسم':
        return Icons.sort_by_alpha;
      case 'المسافة':
        return Icons.location_on;
      default:
        return Icons.sort;
    }
  }
}

class CustomSearchBar extends StatefulWidget {
  const CustomSearchBar({super.key});

  @override
  State<CustomSearchBar> createState() => _CustomSearchBarState();
}

class _CustomSearchBarState extends State<CustomSearchBar> {
  final TextEditingController _controller = TextEditingController();
  Timer? _debounce;

  @override
  void dispose() {
    _controller.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 400), () {
      context.read<HotelsBloc>().add(SearchHotels(query: query));
    });
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: _controller,
      onChanged: _onSearchChanged,
      decoration: InputDecoration(
        hintText: 'Search hotels...',
        prefixIcon: const Icon(Icons.search),
        suffixIcon:
            _controller.text.isNotEmpty
                ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _controller.clear();
                    context.read<HotelsBloc>().add(
                      const SearchHotels(query: ''),
                    );
                  },
                )
                : null,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}

class LoadingWidget extends StatelessWidget {
  final Color? color;

  const LoadingWidget({super.key, this.color});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: CircularProgressIndicator(
        color: color ?? Theme.of(context).primaryColor,
      ),
    );
  }
}

class CustomErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback onRetry;

  const CustomErrorWidget({
    super.key,
    required this.message,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.redAccent),
            const SizedBox(height: 16),
            Text(
              message,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
