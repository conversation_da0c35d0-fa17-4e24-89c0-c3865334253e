import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled7/core/constants/color_constants.dart';
import '../bloc/hotels_bloc.dart';
import '../widgets/hotel_card.dart';

class HotelsListPage extends StatefulWidget {
  const HotelsListPage({super.key});

  @override
  State<HotelsListPage> createState() => _HotelsListPageState();
}

class _HotelsListPageState extends State<HotelsListPage> {
  @override
  void initState() {
    super.initState();
    // Load hotels on page open
    context.read<HotelsBloc>().add(LoadHotels());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Hotels',style: TextStyle(fontSize: 30,fontWeight: FontWeight.bold ,color: Colors.white),),
        backgroundColor: ColorConstants.primaryColor,
        foregroundColor: ColorConstants.onPrimaryColor,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              Navigator.of(context).pushNamed('/filter-hotels');
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: const CustomSearchBar(),
          ),
          Expanded(
            child: BlocBuilder<HotelsBloc, HotelsState>(
              builder: (context, state) {
                if (state is HotelsLoading) {
                  return const LoadingWidget();
                } else if (state is HotelsError) {
                  return CustomErrorWidget(
                    message: state.message,
                    onRetry: () {
                      context.read<HotelsBloc>().add(LoadHotels());
                    },
                  );
                } else if (state is HotelsLoaded) {
                  if (state.hotels.isEmpty) {
                    return const Center(child: Text('No hotels found.'));
                  }

                  return ListView.separated(
                    padding: const EdgeInsets.all(16),
                    itemCount: state.hotels.length,
                    separatorBuilder:
                        (context, index) => const SizedBox(height: 16),
                    itemBuilder: (context, index) {
                      final hotel = state.hotels[index];
                      return HotelCard(
                        hotel: hotel,
                        onTap: () {
                          Navigator.of(
                            context,
                          ).pushNamed('/hotel-details', arguments: hotel.id);
                        },
                      );
                    },
                  );
                }

                return const SizedBox.shrink();
              },
            ),
          ),
        ],
      ),
    );
  }
}

class CustomSearchBar extends StatefulWidget {
  const CustomSearchBar({super.key});

  @override
  State<CustomSearchBar> createState() => _CustomSearchBarState();
}

class _CustomSearchBarState extends State<CustomSearchBar> {
  final TextEditingController _controller = TextEditingController();
  Timer? _debounce;

  @override
  void dispose() {
    _controller.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 400), () {
      context.read<HotelsBloc>().add(SearchHotels(query: query));
    });
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: _controller,
      onChanged: _onSearchChanged,
      decoration: InputDecoration(
        hintText: 'Search hotels...',
        prefixIcon: const Icon(Icons.search),
        suffixIcon:
            _controller.text.isNotEmpty
                ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _controller.clear();
                    context.read<HotelsBloc>().add(const SearchHotels(query: ''));
                  },
                )
                : null,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}

class LoadingWidget extends StatelessWidget {
  final Color? color;

  const LoadingWidget({super.key, this.color});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: CircularProgressIndicator(
        color: color ?? Theme.of(context).primaryColor,
      ),
    );
  }
}

class CustomErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback onRetry;

  const CustomErrorWidget({
    super.key,
    required this.message,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.redAccent),
            const SizedBox(height: 16),
            Text(
              message,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
