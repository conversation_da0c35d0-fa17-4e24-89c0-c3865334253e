import 'package:shared_preferences/shared_preferences.dart';

class FavoritesService {
  static const String _favoritesKey = 'favorite_hotels';
  
  // Get all favorite hotel IDs
  Future<List<String>> getFavoriteIds() async {
    final prefs = await SharedPreferences.getInstance();
    final favoritesJson = prefs.getStringList(_favoritesKey) ?? [];
    return favoritesJson;
  }
  
  // Add hotel to favorites
  Future<bool> addToFavorites(String hotelId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favorites = await getFavoriteIds();
      
      if (!favorites.contains(hotelId)) {
        favorites.add(hotelId);
        await prefs.setStringList(_favoritesKey, favorites);
        return true;
      }
      return false; // Already in favorites
    } catch (e) {
      return false;
    }
  }
  
  // Remove hotel from favorites
  Future<bool> removeFromFavorites(String hotelId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favorites = await getFavoriteIds();
      
      if (favorites.contains(hotelId)) {
        favorites.remove(hotelId);
        await prefs.setStringList(_favoritesKey, favorites);
        return true;
      }
      return false; // Not in favorites
    } catch (e) {
      return false;
    }
  }
  
  // Check if hotel is in favorites
  Future<bool> isFavorite(String hotelId) async {
    final favorites = await getFavoriteIds();
    return favorites.contains(hotelId);
  }
  
  // Toggle favorite status
  Future<bool> toggleFavorite(String hotelId) async {
    final isFav = await isFavorite(hotelId);
    if (isFav) {
      return await removeFromFavorites(hotelId);
    } else {
      return await addToFavorites(hotelId);
    }
  }
  
  // Clear all favorites
  Future<bool> clearAllFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_favoritesKey);
      return true;
    } catch (e) {
      return false;
    }
  }
}
