import 'package:dartz/dartz.dart';
import 'package:untitled7/core/error/failures.dart';
import 'package:untitled7/core/usecases/usecase.dart';
import '../entities/hotel.dart';
import '../repositories/hotels_repository.dart';

class GetHotels implements UseCase<List<Hotel>, NoParams> {
  final HotelsRepository repository;

  GetHotels(this.repository);

  @override
  Future<Either<Failure, List<Hotel>>> call(NoParams params) async {
    return await repository.getHotels();
  }
}
