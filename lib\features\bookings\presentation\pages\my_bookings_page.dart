import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../../../core/constants/color_constants.dart';
import '../bloc/booking_bloc.dart';

// Booking status enum
enum BookingStatus { upcoming, current, past }

class MyBookingsPage extends StatefulWidget {
  const MyBookingsPage({super.key});

  @override
  State<MyBookingsPage> createState() => _MyBookingsPageState();
}

class _MyBookingsPageState extends State<MyBookingsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    context.read<BookingBloc>().add(LoadBookings());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'حجوزاتي',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: ColorConstants.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'القادمة'),
            Tab(text: 'الحالية'),
            Tab(text: 'السابقة'),
          ],
        ),
      ),
      body: BlocBuilder<BookingBloc, BookingState>(
        builder: (context, state) {
          if (state is BookingLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          } else if (state is BookingError) {
            return _buildErrorWidget(state.message);
          } else if (state is BookingsLoaded) {
            return TabBarView(
              controller: _tabController,
              children: [
                _buildBookingsList(_getUpcomingBookings(state.bookings)),
                _buildBookingsList(_getCurrentBookings(state.bookings)),
                _buildBookingsList(_getPastBookings(state.bookings)),
              ],
            );
          }
          return const Center(
            child: Text('لا توجد حجوزات'),
          );
        },
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.redAccent,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                context.read<BookingBloc>().add(LoadBookings());
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorConstants.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBookingsList(List<dynamic> bookings) {
    if (bookings.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.hotel_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد حجوزات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: bookings.length,
      separatorBuilder: (context, index) => const SizedBox(height: 16),
      itemBuilder: (context, index) {
        final booking = bookings[index];
        return _buildBookingCard(booking);
      },
    );
  }

  Widget _buildBookingCard(dynamic booking) {
    // Mock data structure - replace with actual booking model
    final hotelName = booking['hotelName'] ?? 'فندق غير محدد';
    final checkInDate = DateTime.tryParse(booking['checkInDate'] ?? '') ?? DateTime.now();
    final checkOutDate = DateTime.tryParse(booking['checkOutDate'] ?? '') ?? DateTime.now();
    final totalPrice = booking['totalPrice']?.toDouble() ?? 0.0;
    final status = _getBookingStatus(checkInDate, checkOutDate);
    final guestCount = booking['guestCount'] ?? 1;
    final roomCount = booking['roomCount'] ?? 1;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _getStatusColor(status).withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  hotelName,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(status),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    _getStatusText(status),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        Icons.calendar_today,
                        'تاريخ الوصول',
                        DateFormat('dd/MM/yyyy').format(checkInDate),
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        Icons.calendar_today_outlined,
                        'تاريخ المغادرة',
                        DateFormat('dd/MM/yyyy').format(checkOutDate),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        Icons.people,
                        'عدد الضيوف',
                        '$guestCount ضيف',
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        Icons.hotel,
                        'عدد الغرف',
                        '$roomCount غرفة',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'المجموع الكلي',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: ColorConstants.primaryColor,
                      ),
                    ),
                    Text(
                      '${totalPrice.toStringAsFixed(0)} ج.م',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: ColorConstants.primaryColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          _showBookingDetails(booking);
                        },
                        icon: const Icon(Icons.info_outline),
                        label: const Text('التفاصيل'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: ColorConstants.primaryColor,
                          side: BorderSide(color: ColorConstants.primaryColor),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    if (status == BookingStatus.upcoming)
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            _showCancelDialog(booking);
                          },
                          icon: const Icon(Icons.cancel),
                          label: const Text('إلغاء'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Helper methods for filtering bookings
  List<dynamic> _getUpcomingBookings(List<dynamic> bookings) {
    final now = DateTime.now();
    return bookings.where((booking) {
      final checkInDate = DateTime.tryParse(booking['checkInDate'] ?? '') ?? DateTime.now();
      return checkInDate.isAfter(now);
    }).toList();
  }

  List<dynamic> _getCurrentBookings(List<dynamic> bookings) {
    final now = DateTime.now();
    return bookings.where((booking) {
      final checkInDate = DateTime.tryParse(booking['checkInDate'] ?? '') ?? DateTime.now();
      final checkOutDate = DateTime.tryParse(booking['checkOutDate'] ?? '') ?? DateTime.now();
      return checkInDate.isBefore(now) && checkOutDate.isAfter(now);
    }).toList();
  }

  List<dynamic> _getPastBookings(List<dynamic> bookings) {
    final now = DateTime.now();
    return bookings.where((booking) {
      final checkOutDate = DateTime.tryParse(booking['checkOutDate'] ?? '') ?? DateTime.now();
      return checkOutDate.isBefore(now);
    }).toList();
  }

  BookingStatus _getBookingStatus(DateTime checkInDate, DateTime checkOutDate) {
    final now = DateTime.now();
    if (checkInDate.isAfter(now)) {
      return BookingStatus.upcoming;
    } else if (checkOutDate.isAfter(now)) {
      return BookingStatus.current;
    } else {
      return BookingStatus.past;
    }
  }

  Color _getStatusColor(BookingStatus status) {
    switch (status) {
      case BookingStatus.upcoming:
        return Colors.blue;
      case BookingStatus.current:
        return Colors.green;
      case BookingStatus.past:
        return Colors.grey;
    }
  }

  String _getStatusText(BookingStatus status) {
    switch (status) {
      case BookingStatus.upcoming:
        return 'قادم';
      case BookingStatus.current:
        return 'حالي';
      case BookingStatus.past:
        return 'منتهي';
    }
  }

  void _showBookingDetails(dynamic booking) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تفاصيل الحجز',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: ColorConstants.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 20),
                    // Add more booking details here
                    Text(
                      'اسم الفندق: ${booking['hotelName'] ?? 'غير محدد'}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'اسم الضيف: ${booking['guestName'] ?? 'غير محدد'}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'البريد الإلكتروني: ${booking['guestEmail'] ?? 'غير محدد'}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'رقم الهاتف: ${booking['guestPhone'] ?? 'غير محدد'}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    if (booking['notes'] != null && booking['notes'].isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text(
                        'ملاحظات: ${booking['notes']}',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCancelDialog(dynamic booking) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء الحجز'),
        content: const Text('هل أنت متأكد من رغبتك في إلغاء هذا الحجز؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement cancel booking logic
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إلغاء الحجز بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('تأكيد الإلغاء'),
          ),
        ],
      ),
    );
  }
}