import 'package:equatable/equatable.dart';

enum BookingStatus { pending, confirmed, cancelled, completed }

class Booking extends Equatable {
  final String id;
  final String userId;
  final String hotelId;
  final String roomId;
  final DateTime checkInDate;
  final DateTime checkOutDate;
  final int guestCount;
  final double totalPrice;
  final BookingStatus status;
  final DateTime createdAt;
  final String? notes;

  const Booking({
    required this.id,
    required this.userId,
    required this.hotelId,
    required this.roomId,
    required this.checkInDate,
    required this.checkOutDate,
    required this.guestCount,
    required this.totalPrice,
    required this.status,
    required this.createdAt,
    this.notes,
  });

  int get nightCount {
    return checkOutDate.difference(checkInDate).inDays;
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        hotelId,
        roomId,
        checkInDate,
        checkOutDate,
        guestCount,
        totalPrice,
        status,
        createdAt,
        notes,
      ];
}
