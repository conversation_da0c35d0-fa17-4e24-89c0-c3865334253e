import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class BookingsService {
  static const String _bookingsKey = 'user_bookings';
  
  // Get all bookings
  Future<List<Map<String, dynamic>>> getBookings() async {
    final prefs = await SharedPreferences.getInstance();
    final bookingsJson = prefs.getStringList(_bookingsKey) ?? [];
    return bookingsJson.map((json) => jsonDecode(json) as Map<String, dynamic>).toList();
  }
  
  // Add new booking
  Future<bool> addBooking(Map<String, dynamic> bookingData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final bookings = await getBookings();
      
      // Add booking ID and timestamp
      bookingData['id'] = DateTime.now().millisecondsSinceEpoch.toString();
      bookingData['createdAt'] = DateTime.now().toIso8601String();
      bookingData['status'] = 'confirmed';
      
      bookings.add(bookingData);
      
      final bookingsJson = bookings.map((booking) => jsonEncode(booking)).toList();
      await prefs.setStringList(_bookingsKey, bookingsJson);
      return true;
    } catch (e) {
      return false;
    }
  }
  
  // Update booking
  Future<bool> updateBooking(String bookingId, Map<String, dynamic> updatedData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final bookings = await getBookings();
      
      final index = bookings.indexWhere((booking) => booking['id'] == bookingId);
      if (index != -1) {
        bookings[index] = {...bookings[index], ...updatedData};
        final bookingsJson = bookings.map((booking) => jsonEncode(booking)).toList();
        await prefs.setStringList(_bookingsKey, bookingsJson);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
  
  // Cancel booking
  Future<bool> cancelBooking(String bookingId) async {
    return await updateBooking(bookingId, {'status': 'cancelled'});
  }
  
  // Get booking by ID
  Future<Map<String, dynamic>?> getBookingById(String bookingId) async {
    final bookings = await getBookings();
    try {
      return bookings.firstWhere((booking) => booking['id'] == bookingId);
    } catch (e) {
      return null;
    }
  }
  
  // Get upcoming bookings
  Future<List<Map<String, dynamic>>> getUpcomingBookings() async {
    final bookings = await getBookings();
    final now = DateTime.now();
    
    return bookings.where((booking) {
      final checkInDate = DateTime.tryParse(booking['checkInDate'] ?? '') ?? DateTime.now();
      return checkInDate.isAfter(now) && booking['status'] != 'cancelled';
    }).toList();
  }
  
  // Get current bookings
  Future<List<Map<String, dynamic>>> getCurrentBookings() async {
    final bookings = await getBookings();
    final now = DateTime.now();
    
    return bookings.where((booking) {
      final checkInDate = DateTime.tryParse(booking['checkInDate'] ?? '') ?? DateTime.now();
      final checkOutDate = DateTime.tryParse(booking['checkOutDate'] ?? '') ?? DateTime.now();
      return checkInDate.isBefore(now) && 
             checkOutDate.isAfter(now) && 
             booking['status'] != 'cancelled';
    }).toList();
  }
  
  // Get past bookings
  Future<List<Map<String, dynamic>>> getPastBookings() async {
    final bookings = await getBookings();
    final now = DateTime.now();
    
    return bookings.where((booking) {
      final checkOutDate = DateTime.tryParse(booking['checkOutDate'] ?? '') ?? DateTime.now();
      return checkOutDate.isBefore(now) || booking['status'] == 'cancelled';
    }).toList();
  }
  
  // Clear all bookings
  Future<bool> clearAllBookings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_bookingsKey);
      return true;
    } catch (e) {
      return false;
    }
  }
}
