import 'dart:convert';
import 'package:http/http.dart' as http;

class BookingsRemoteDataSource {
  final String baseUrl;
  final http.Client client;
  
  BookingsRemoteDataSource({
    required this.baseUrl,
    http.Client? client,
  }) : client = client ?? http.Client();

  static const String _bookingsEndpoint = '/api/bookings';
  
  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  /// Fetch all bookings
  Future<List<Map<String, dynamic>>> getBookings({
    int? page,
    int? limit,
    String? status,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final queryParams = <String, String>{};
    
    if (page != null) queryParams['page'] = page.toString();
    if (limit != null) queryParams['limit'] = limit.toString();
    if (status != null) queryParams['status'] = status;
    if (startDate != null) queryParams['start_date'] = startDate.toIso8601String();
    if (endDate != null) queryParams['end_date'] = endDate.toIso8601String();
    
    final uri = Uri.parse('$baseUrl$_bookingsEndpoint')
        .replace(queryParameters: queryParams);
    
    final response = await client.get(uri, headers: _headers);
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return List<Map<String, dynamic>>.from(data['bookings'] ?? data);
    } else {
      throw BookingRemoteException(
        'Failed to fetch bookings: ${response.statusCode}',
        response.statusCode,
      );
    }
  }

  /// Fetch a single booking by ID
  Future<Map<String, dynamic>> getBookingById(String id) async {
    final uri = Uri.parse('$baseUrl$_bookingsEndpoint/$id');
    
    final response = await client.get(uri, headers: _headers);
    
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else if (response.statusCode == 404) {
      throw BookingNotFoundException('Booking with ID $id not found');
    } else {
      throw BookingRemoteException(
        'Failed to fetch booking: ${response.statusCode}',
        response.statusCode,
      );
    }
  }

  /// Create a new booking
  Future<Map<String, dynamic>> createBooking(Map<String, dynamic> bookingData) async {
    final uri = Uri.parse('$baseUrl$_bookingsEndpoint');
    
    final response = await client.post(
      uri,
      headers: _headers,
      body: json.encode(bookingData),
    );
    
    if (response.statusCode == 201) {
      return json.decode(response.body);
    } else if (response.statusCode == 400) {
      final errorData = json.decode(response.body);
      throw BookingValidationException(
        'Validation failed: ${errorData['message'] ?? 'Invalid data'}',
        errorData['errors'],
      );
    } else {
      throw BookingRemoteException(
        'Failed to create booking: ${response.statusCode}',
        response.statusCode,
      );
    }
  }

  /// Update an existing booking
  Future<Map<String, dynamic>> updateBooking(String id, Map<String, dynamic> bookingData) async {
    final uri = Uri.parse('$baseUrl$_bookingsEndpoint/$id');
    
    final response = await client.put(
      uri,
      headers: _headers,
      body: json.encode(bookingData),
    );
    
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else if (response.statusCode == 404) {
      throw BookingNotFoundException('Booking with ID $id not found');
    } else if (response.statusCode == 400) {
      final errorData = json.decode(response.body);
      throw BookingValidationException(
        'Validation failed: ${errorData['message'] ?? 'Invalid data'}',
        errorData['errors'],
      );
    } else {
      throw BookingRemoteException(
        'Failed to update booking: ${response.statusCode}',
        response.statusCode,
      );
    }
  }

  /// Delete a booking
  Future<void> deleteBooking(String id) async {
    final uri = Uri.parse('$baseUrl$_bookingsEndpoint/$id');
    
    final response = await client.delete(uri, headers: _headers);
    
    if (response.statusCode == 204) {
      return;
    } else if (response.statusCode == 404) {
      throw BookingNotFoundException('Booking with ID $id not found');
    } else {
      throw BookingRemoteException(
        'Failed to delete booking: ${response.statusCode}',
        response.statusCode,
      );
    }
  }

  /// Cancel a booking
  Future<Map<String, dynamic>> cancelBooking(String id, {String? reason}) async {
    final uri = Uri.parse('$baseUrl$_bookingsEndpoint/$id/cancel');
    
    final body = reason != null ? {'reason': reason} : <String, dynamic>{};
    
    final response = await client.post(
      uri,
      headers: _headers,
      body: json.encode(body),
    );
    
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else if (response.statusCode == 404) {
      throw BookingNotFoundException('Booking with ID $id not found');
    } else {
      throw BookingRemoteException(
        'Failed to cancel booking: ${response.statusCode}',
        response.statusCode,
      );
    }
  }

  /// Get user's bookings
  Future<List<Map<String, dynamic>>> getUserBookings(String userId) async {
    final uri = Uri.parse('$baseUrl/api/users/$userId/bookings');
    
    final response = await client.get(uri, headers: _headers);
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return List<Map<String, dynamic>>.from(data['bookings'] ?? data);
    } else {
      throw BookingRemoteException(
        'Failed to fetch user bookings: ${response.statusCode}',
        response.statusCode,
      );
    }
  }

  /// Search bookings
  Future<List<Map<String, dynamic>>> searchBookings(String query) async {
    final uri = Uri.parse('$baseUrl$_bookingsEndpoint/search')
        .replace(queryParameters: {'q': query});
    
    final response = await client.get(uri, headers: _headers);
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return List<Map<String, dynamic>>.from(data['bookings'] ?? data);
    } else {
      throw BookingRemoteException(
        'Failed to search bookings: ${response.statusCode}',
        response.statusCode,
      );
    }
  }

  void dispose() {
    client.close();
  }
}

// Custom exceptions
class BookingRemoteException implements Exception {
  final String message;
  final int statusCode;
  
  BookingRemoteException(this.message, this.statusCode);
  
  @override
  String toString() => 'BookingRemoteException: $message (Status: $statusCode)';
}

class BookingNotFoundException extends BookingRemoteException {
  BookingNotFoundException(String message) : super(message, 404);
}

class BookingValidationException extends BookingRemoteException {
  final Map<String, dynamic>? errors;
  
  BookingValidationException(String message, this.errors) : super(message, 400);
  
  @override
  String toString() => 'BookingValidationException: $message${errors != null ? ' - $errors' : ''}';
}