import 'package:flutter/material.dart';
import 'package:untitled7/core/constants/color_constants.dart';

class RatingStars extends StatelessWidget {
  final double rating;
  final double size;

  const RatingStars({
    super.key,
    required this.rating,
    this.size = 16,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        if (index < rating.floor()) {
          return Icon(
            Icons.star,
            size: size,
            color: Colors.amber,
          );
        } else if (index < rating) {
          return Icon(
            Icons.star_half,
            size: size,
            color: Colors.amber,
          );
        } else {
          return Icon(
            Icons.star_border,
            size: size,
            color: ColorConstants.greyColor,
          );
        }
      }),
    );
  }
}

