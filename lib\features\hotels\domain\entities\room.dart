import 'package:equatable/equatable.dart';

class Room extends Equatable {
  final String id;
  final String name;
  final String description;
  final String type;
  final double price;
  final int maxGuests;
  final int bedCount;
  final String bedType;
  final double size;
  final List<String> images;
  final List<String> amenities;
  final bool isAvailable;

  const Room({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.price,
    required this.maxGuests,
    required this.bedCount,
    required this.bedType,
    required this.size,
    required this.images,
    required this.amenities,
    required this.isAvailable,
  });

  @override
  List<Object> get props => [
        id,
        name,
        description,
        type,
        price,
        maxGuests,
        bedCount,
        bedType,
        size,
        images,
        amenities,
        isAvailable,
      ];
}
