import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:untitled7/core/error/failures.dart';
import 'package:untitled7/core/usecases/usecase.dart';
import '../entities/booking.dart';
import '../repositories/bookings_repository.dart';

class CreateBooking implements UseCase<Booking, CreateBookingParams> {
  final BookingsRepository repository;

  CreateBooking(this.repository);

  @override
  Future<Either<Failure, Booking>> call(CreateBookingParams params) async {
    return await repository.createBooking(
      hotelId: params.hotelId,
      roomId: params.roomId,
      checkInDate: params.checkInDate,
      checkOutDate: params.checkOutDate,
      guestCount: params.guestCount,
      notes: params.notes,
    );
  }
}

class CreateBookingParams extends Equatable {
  final String hotelId;
  final String roomId;
  final DateTime checkInDate;
  final DateTime checkOutDate;
  final int guestCount;
  final String? notes;

  const CreateBookingParams({
    required this.hotelId,
    required this.roomId,
    required this.checkInDate,
    required this.checkOutDate,
    required this.guestCount,
    this.notes,
  });

  @override
  List<Object?> get props => [
        hotelId,
        roomId,
        checkInDate,
        checkOutDate,
        guestCount,
        notes,
      ];
}