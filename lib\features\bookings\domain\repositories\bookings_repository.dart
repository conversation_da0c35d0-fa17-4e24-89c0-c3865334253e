import 'package:dartz/dartz.dart';
import 'package:untitled7/core/error/failures.dart';
import '../entities/booking.dart';

abstract class BookingsRepository {
  Future<Either<Failure, Booking>> createBooking({
    required String hotelId,
    required String roomId,
    required DateTime checkInDate,
    required DateTime checkOutDate,
    required int guestCount,
    String? notes,
  });
  
  Future<Either<Failure, List<Booking>>> getUserBookings(String userId);
  Future<Either<Failure, void>> cancelBooking(String bookingId);
  Future<Either<Failure, Booking>> getBookingById(String bookingId);
}