import 'package:bloc/bloc.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:equatable/equatable.dart';

part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final FirebaseAuth _firebaseAuth;

  AuthBloc({FirebaseAuth? firebaseAuth})
      : _firebaseAuth = firebaseAuth ?? FirebaseAuth.instance,
        super(AuthInitial()) {
    on<AuthLoginRequested>(_onLoginRequested);
    on<AuthRegisterRequested>(_onRegisterRequested);
  }

  Future<void> _onLoginRequested(
      AuthLoginRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      await _firebaseAuth.signInWithEmailAndPassword(
        email: event.email,
        password: event.password,
      );
      emit(AuthAuthenticated());
    } on FirebaseAuthException catch (e) {
      emit(AuthError(message: e.message ?? 'حدث خطأ أثناء تسجيل الدخول'));
    } catch (_) {
      emit(AuthError(message: 'حدث خطأ غير متوقع'));
    }
  }

  Future<void> _onRegisterRequested(
      AuthRegisterRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      await _firebaseAuth.createUserWithEmailAndPassword(
        email: event.email,
        password: event.password,
      );

      // Update the user's display name
      await _firebaseAuth.currentUser?.updateDisplayName(event.name);

      emit(AuthAuthenticated());
    } on FirebaseAuthException catch (e) {
      emit(AuthError(message: e.message ?? 'حدث خطأ أثناء إنشاء الحساب'));
    } catch (_) {
      emit(AuthError(message: 'حدث خطأ غير متوقع'));
    }
  }
}
