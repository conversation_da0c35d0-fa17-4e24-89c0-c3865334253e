import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

// Events
abstract class BookingEvent extends Equatable {
  const BookingEvent();

  @override
  List<Object> get props => [];
}

class LoadBookings extends BookingEvent {}

class CreateBooking extends BookingEvent {
  final Map<String, dynamic> bookingData;

  const CreateBooking({required this.bookingData});

  @override
  List<Object> get props => [bookingData];
}

// States
abstract class BookingState extends Equatable {
  const BookingState();

  @override
  List<Object> get props => [];
}

class BookingInitial extends BookingState {}

class BookingLoading extends BookingState {}

class BookingsLoaded extends BookingState {
  final List<dynamic> bookings; // Replace with your Booking model

  const BookingsLoaded({required this.bookings});

  @override
  List<Object> get props => [bookings];
}

class BookingCreated extends BookingState {
  final dynamic booking; // Replace with your Booking model

  const BookingCreated({required this.booking});

  @override
  List<Object> get props => [booking];
}

class BookingError extends BookingState {
  final String message;

  const BookingError({required this.message});

  @override
  List<Object> get props => [message];
}

// Bloc
class BookingBloc extends Bloc<BookingEvent, BookingState> {
  BookingBloc() : super(BookingInitial()) {
    on<LoadBookings>(_onLoadBookings);
    on<CreateBooking>(_onCreateBooking);
  }

  void _onLoadBookings(LoadBookings event, Emitter<BookingState> emit) async {
    emit(BookingLoading());
    try {
      // Implement load bookings logic here
      await Future.delayed(const Duration(seconds: 1));
      emit(const BookingsLoaded(bookings: []));
    } catch (e) {
      emit(BookingError(message: e.toString()));
    }
  }

  void _onCreateBooking(CreateBooking event, Emitter<BookingState> emit) async {
    emit(BookingLoading());
    try {
      // Implement create booking logic here
      await Future.delayed(const Duration(seconds: 1));
      emit(const BookingCreated(booking: {}));
    } catch (e) {
      emit(BookingError(message: e.toString()));
    }
  }
}