import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

// Events
abstract class HotelsEvent extends Equatable {
  const HotelsEvent();

  @override
  List<Object> get props => [];
}

class LoadHotels extends HotelsEvent {}

class SearchHotels extends HotelsEvent {
  final String query;

  const SearchHotels({required this.query});

  @override
  List<Object> get props => [query];
}

// States
abstract class HotelsState extends Equatable {
  const HotelsState();

  @override
  List<Object> get props => [];
}

class HotelsInitial extends HotelsState {}

class HotelsLoading extends HotelsState {}

class HotelsLoaded extends HotelsState {
  final List<dynamic> hotels; // Replace with your Hotel model

  const HotelsLoaded({required this.hotels});

  @override
  List<Object> get props => [hotels];
}

class HotelsError extends HotelsState {
  final String message;

  const HotelsError({required this.message});

  @override
  List<Object> get props => [message];
}

// Bloc
class HotelsBloc extends Bloc<HotelsEvent, HotelsState> {
  HotelsBloc() : super(HotelsInitial()) {
    on<LoadHotels>(_onLoadHotels);
    on<SearchHotels>(_onSearchHotels);
  }

  void _onLoadHotels(LoadHotels event, Emitter<HotelsState> emit) async {
    emit(HotelsLoading());
    try {
      // Implement load hotels logic here
      await Future.delayed(const Duration(seconds: 1));
      emit(const HotelsLoaded(hotels: []));
    } catch (e) {
      emit(HotelsError(message: e.toString()));
    }
  }

  void _onSearchHotels(SearchHotels event, Emitter<HotelsState> emit) async {
    emit(HotelsLoading());
    try {
      // Implement search hotels logic here
      await Future.delayed(const Duration(seconds: 1));
      emit(const HotelsLoaded(hotels: []));
    } catch (e) {
      emit(HotelsError(message: e.toString()));
    }
  }
}