import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../domain/entities/hotel.dart';
import '../../data/datasources/hotels_api_service.dart';

// Events
abstract class HotelsEvent extends Equatable {
  const HotelsEvent();

  @override
  List<Object> get props => [];
}

class LoadHotels extends HotelsEvent {}

class SearchHotels extends HotelsEvent {
  final String query;

  const SearchHotels({required this.query});

  @override
  List<Object> get props => [query];
}

class FilterHotels extends HotelsEvent {
  final String? query;
  final String? location;
  final double? minPrice;
  final double? maxPrice;
  final double? minRating;
  final List<String>? amenities;

  const FilterHotels({
    this.query,
    this.location,
    this.minPrice,
    this.maxPrice,
    this.minRating,
    this.amenities,
  });

  @override
  List<Object> get props => [
    query ?? '',
    location ?? '',
    minPrice ?? 0,
    maxPrice ?? 0,
    minRating ?? 0,
    amenities ?? [],
  ];
}

// States
abstract class HotelsState extends Equatable {
  const HotelsState();

  @override
  List<Object> get props => [];
}

class HotelsInitial extends HotelsState {}

class HotelsLoading extends HotelsState {}

class HotelsLoaded extends HotelsState {
  final List<Hotel> hotels;

  const HotelsLoaded({required this.hotels});

  @override
  List<Object> get props => [hotels];
}

class HotelsError extends HotelsState {
  final String message;

  const HotelsError({required this.message});

  @override
  List<Object> get props => [message];
}

// Bloc
class HotelsBloc extends Bloc<HotelsEvent, HotelsState> {
  final HotelsApiService _apiService;

  HotelsBloc(this._apiService) : super(HotelsInitial()) {
    on<LoadHotels>(_onLoadHotels);
    on<SearchHotels>(_onSearchHotels);
    on<FilterHotels>(_onFilterHotels);
  }

  void _onLoadHotels(LoadHotels event, Emitter<HotelsState> emit) async {
    emit(HotelsLoading());
    try {
      final hotelsData = await _apiService.getHotels();
      final hotels = hotelsData.map((data) => Hotel.fromJson(data)).toList();
      emit(HotelsLoaded(hotels: hotels));
    } catch (e) {
      emit(HotelsError(message: 'فشل في تحميل الفنادق: ${e.toString()}'));
    }
  }

  void _onSearchHotels(SearchHotels event, Emitter<HotelsState> emit) async {
    emit(HotelsLoading());
    try {
      final hotelsData = await _apiService.searchHotels(query: event.query);
      final hotels = hotelsData.map((data) => Hotel.fromJson(data)).toList();
      emit(HotelsLoaded(hotels: hotels));
    } catch (e) {
      emit(HotelsError(message: 'فشل في البحث عن الفنادق: ${e.toString()}'));
    }
  }

  void _onFilterHotels(FilterHotels event, Emitter<HotelsState> emit) async {
    emit(HotelsLoading());
    try {
      final hotelsData = await _apiService.searchHotels(
        query: event.query,
        location: event.location,
        minPrice: event.minPrice,
        maxPrice: event.maxPrice,
        minRating: event.minRating,
        amenities: event.amenities,
      );
      final hotels = hotelsData.map((data) => Hotel.fromJson(data)).toList();
      emit(HotelsLoaded(hotels: hotels));
    } catch (e) {
      emit(HotelsError(message: 'فشل في فلترة الفنادق: ${e.toString()}'));
    }
  }
}