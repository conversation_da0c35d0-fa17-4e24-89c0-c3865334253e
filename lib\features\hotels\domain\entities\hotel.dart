import 'package:equatable/equatable.dart';
import 'room.dart';
import 'amenity.dart';

class Hotel extends Equatable {
  final String id;
  final String name;
  final String description;
  final String address;
  final String city;
  final String country;
  final double latitude;
  final double longitude;
  final double rating;
  final int reviewCount;
  final List<String> images;
  final List<Room> rooms;
  final List<Amenity> amenities;
  final double priceFrom;
  final bool isAvailable;

  const Hotel({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.city,
    required this.country,
    required this.latitude,
    required this.longitude,
    required this.rating,
    required this.reviewCount,
    required this.images,
    required this.rooms,
    required this.amenities,
    required this.priceFrom,
    required this.isAvailable,
  });

  @override
  List<Object> get props => [
        id,
        name,
        description,
        address,
        city,
        country,
        latitude,
        longitude,
        rating,
        reviewCount,
        images,
        rooms,
        amenities,
        priceFrom,
        isAvailable,
      ];
}
