import 'package:equatable/equatable.dart';

class Hotel extends Equatable {
  final String id;
  final String name;
  final String description;
  final String address;
  final String location;
  final double latitude;
  final double longitude;
  final double rating;
  final List<String> images;
  final List<String> amenities;
  final double price;
  final String imageUrl;
  final String phone;
  final String email;
  final String checkInTime;
  final String checkOutTime;
  final int totalRooms;
  final int availableRooms;
  final String category;

  const Hotel({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.location,
    required this.latitude,
    required this.longitude,
    required this.rating,
    required this.images,
    required this.amenities,
    required this.price,
    required this.imageUrl,
    required this.phone,
    required this.email,
    required this.checkInTime,
    required this.checkOutTime,
    required this.totalRooms,
    required this.availableRooms,
    required this.category,
  });

  factory Hotel.fromJson(Map<String, dynamic> json) {
    return Hotel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      address: json['address'] ?? '',
      location: json['location'] ?? '',
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      rating: (json['rating'] ?? 0.0).toDouble(),
      images: List<String>.from(json['images'] ?? []),
      amenities: List<String>.from(json['amenities'] ?? []),
      price: (json['price'] ?? 0.0).toDouble(),
      imageUrl: json['imageUrl'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'] ?? '',
      checkInTime: json['checkInTime'] ?? '',
      checkOutTime: json['checkOutTime'] ?? '',
      totalRooms: json['totalRooms'] ?? 0,
      availableRooms: json['availableRooms'] ?? 0,
      category: json['category'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'address': address,
      'location': location,
      'latitude': latitude,
      'longitude': longitude,
      'rating': rating,
      'images': images,
      'amenities': amenities,
      'price': price,
      'imageUrl': imageUrl,
      'phone': phone,
      'email': email,
      'checkInTime': checkInTime,
      'checkOutTime': checkOutTime,
      'totalRooms': totalRooms,
      'availableRooms': availableRooms,
      'category': category,
    };
  }

  @override
  List<Object> get props => [
        id,
        name,
        description,
        address,
        location,
        latitude,
        longitude,
        rating,
        images,
        amenities,
        price,
        imageUrl,
        phone,
        email,
        checkInTime,
        checkOutTime,
        totalRooms,
        availableRooms,
        category,
      ];
}
