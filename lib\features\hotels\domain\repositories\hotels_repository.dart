import 'package:dartz/dartz.dart';
import 'package:untitled7/core/error/failures.dart';
import '../entities/hotel.dart';

abstract class HotelsRepository {
  Future<Either<Failure, List<Hotel>>> getHotels();
  Future<Either<Failure, Hotel>> getHotelById(String id);
  Future<Either<Failure, List<Hotel>>> searchHotels(String query);
  Future<Either<Failure, List<Hotel>>> filterHotels({
    double? minPrice,
    double? maxPrice,
    double? minRating,
    List<String>? amenities,
  });
}