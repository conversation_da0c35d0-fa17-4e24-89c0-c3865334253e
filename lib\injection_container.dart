import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

import 'core/network/network_info.dart';
import 'core/network/dio_client.dart';
// Import all your blocs and dependencies
import 'features/authentication/presentation/bloc/auth_bloc.dart';
import 'features/hotels/presentation/bloc/hotels_bloc.dart';
import 'features/bookings/presentation/bloc/booking_bloc.dart';
// import 'features/auth/data/datasources/auth_remote_datasource.dart';
// import 'features/auth/data/repositories/auth_repository_impl.dart';
// import 'features/auth/domain/repositories/auth_repository.dart';
// import 'features/auth/domain/usecases/login_usecase.dart';

final GetIt sl = GetIt.instance;

/// Initialize all dependencies
/// Call this method in main() before runApp()
Future<void> initDependencies() async {
  try {
    await _initExternalDependencies();
    _initCoreDependencies();
    _initFeatureDependencies();
    
    if (kDebugMode) {
      print('✅ All dependencies initialized successfully');
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Error initializing dependencies: $e');
    }
    rethrow;
  }
}

/// Initialize external dependencies (third-party packages)
Future<void> _initExternalDependencies() async {
  // SharedPreferences - async initialization
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton<SharedPreferences>(() => sharedPreferences);
  
  // Connectivity
  sl.registerLazySingleton<Connectivity>(() => Connectivity());
  
  // Dio with configuration
  sl.registerLazySingleton<Dio>(() => _createDio());
}

/// Initialize core dependencies
void _initCoreDependencies() {
  // Network Info
  sl.registerLazySingleton<NetworkInfo>(
    () => NetworkInfoImpl(sl<Connectivity>()),
  );
  
  // Dio Client
  sl.registerLazySingleton<DioClient>(
    () => DioClient(sl<Dio>()),
  );
}

/// Configure Dio instance with interceptors and options
Dio _createDio() {
  final dio = Dio();
  
  // Base configuration
  dio.options = BaseOptions(
    baseUrl: 'https://your-api-base-url.com/api/',
    connectTimeout: const Duration(seconds: 30),
    receiveTimeout: const Duration(seconds: 30),
    sendTimeout: const Duration(seconds: 30),
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  );
  
  // Add interceptors
  if (kDebugMode) {
    dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: false,
        error: true,
      ),
    );
  }
  
  // Add auth interceptor
  dio.interceptors.add(
    InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add auth token if available
        final token = sl<SharedPreferences>().getString('auth_token');
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        // Handle token refresh or logout on 401
        if (error.response?.statusCode == 401) {
          // Handle unauthorized access
          await _handleUnauthorized();
        }
        handler.next(error);
      },
    ),
  );
  
  return dio;
}

/// Handle unauthorized access (401 errors)
Future<void> _handleUnauthorized() async {
  // Clear stored tokens
  await sl<SharedPreferences>().remove('auth_token');
  await sl<SharedPreferences>().remove('refresh_token');
  
  // Navigate to login screen or show appropriate message
  // This depends on your navigation setup
}

/// Initialize feature-specific dependencies
void _initFeatureDependencies() {
  _initAuthFeature();
  _initHotelsFeature();
  _initBookingsFeature();
}

void _initAuthFeature() {
  // Data sources
  // sl.registerLazySingleton<AuthRemoteDataSource>(
  //   () => AuthRemoteDataSourceImpl(sl<DioClient>()),
  // );
  
  // Repositories
  // sl.registerLazySingleton<AuthRepository>(
  //   () => AuthRepositoryImpl(
  //     remoteDataSource: sl<AuthRemoteDataSource>(),
  //     networkInfo: sl<NetworkInfo>(),
  //   ),
  // );
  
  // Use cases
  // sl.registerLazySingleton(() => LoginUseCase(sl<AuthRepository>()));
  // sl.registerLazySingleton(() => LogoutUseCase(sl<AuthRepository>()));
  
  // Blocs - Register as Factory (new instance each time)
  sl.registerFactory(() => AuthBloc(
    // Add your required parameters here
    // loginUseCase: sl<LoginUseCase>(),
    // logoutUseCase: sl<LogoutUseCase>(),
  ));
}

void _initHotelsFeature() {
  // Data sources
  // sl.registerLazySingleton<HotelsRemoteDataSource>(
  //   () => HotelsRemoteDataSourceImpl(sl<DioClient>()),
  // );
  
  // Repositories
  // sl.registerLazySingleton<HotelsRepository>(
  //   () => HotelsRepositoryImpl(
  //     remoteDataSource: sl<HotelsRemoteDataSource>(),
  //     networkInfo: sl<NetworkInfo>(),
  //   ),
  // );
  
  // Use cases
  // sl.registerLazySingleton(() => GetHotelsUseCase(sl<HotelsRepository>()));
  // sl.registerLazySingleton(() => SearchHotelsUseCase(sl<HotelsRepository>()));
  
  // Blocs
  sl.registerFactory(() => HotelsBloc(
    // Add your required parameters here
    // getHotelsUseCase: sl<GetHotelsUseCase>(),
    // searchHotelsUseCase: sl<SearchHotelsUseCase>(),
  ));
}

void _initBookingsFeature() {
  // Data sources
  // sl.registerLazySingleton<BookingRemoteDataSource>(
  //   () => BookingRemoteDataSourceImpl(sl<DioClient>()),
  // );
  
  // Repositories
  // sl.registerLazySingleton<BookingRepository>(
  //   () => BookingRepositoryImpl(
  //     remoteDataSource: sl<BookingRemoteDataSource>(),
  //     networkInfo: sl<NetworkInfo>(),
  //   ),
  // );
  
  // Use cases
  // sl.registerLazySingleton(() => CreateBookingUseCase(sl<BookingRepository>()));
  // sl.registerLazySingleton(() => GetBookingsUseCase(sl<BookingRepository>()));
  
  // Blocs
  sl.registerFactory(() => BookingBloc(
    // Add your required parameters here
    // createBookingUseCase: sl<CreateBookingUseCase>(),
    // getBookingsUseCase: sl<GetBookingsUseCase>(),
  ));
}

/// Reset all dependencies (useful for testing)
Future<void> resetDependencies() async {
  await sl.reset();
}

/// Check if a dependency is registered
bool isDependencyRegistered<T extends Object>() {
  return sl.isRegistered<T>();
}

/// Get dependency safely with error handling
T? getDependencySafely<T extends Object>() {
  try {
    return sl.get<T>();
  } catch (e) {
    if (kDebugMode) {
      print('❌ Error getting dependency ${T.toString()}: $e');
    }
    return null;
  }
}